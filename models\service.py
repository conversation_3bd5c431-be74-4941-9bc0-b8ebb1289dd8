from tortoise.models import Model
from tortoise import fields
from typing import List
import uuid
from .appointment import Appointment


class ServiceCategory(Model):
    """Service categories for organizing services"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    name = fields.CharField(max_length=100)
    description = fields.TextField(null=True)
    status = fields.CharField(max_length=50, default="active", null=True)
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)

    # Reverse relations
    services: fields.ReverseRelation["Service"]

    class Meta:
        table = "service_categories"

    @classmethod
    async def get_active_service_categories(cls) -> List["ServiceCategory"]:
        "Get all active service categories"
        return await cls.filter(status="active")

    def __str__(self):
        return self.name


class ServiceSkill(Model):
    """Many-to-many relationship between services and skills"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)

    # Foreign key relations
    service = fields.ForeignKeyField(
        "models.Service", related_name="service_skills", on_delete=fields.CASCADE
    )
    skill = fields.ForeignKeyField(
        "models.Skill", related_name="service_skills", on_delete=fields.CASCADE
    )

    class Meta:
        table = "service_skills"
        unique_together = (("service", "skill"),)


class Service(Model):
    """Services offered"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    name = fields.CharField(max_length=100)
    description = fields.TextField(null=True)
    duration_minutes = fields.IntField()
    benefits = fields.TextField(null=True)
    amount = fields.DecimalField(max_digits=10, decimal_places=2)
    status = fields.CharField(max_length=20, default="active")
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)
    is_featured = fields.BooleanField(default=False, null=True)
    discount = fields.DecimalField(max_digits=10, decimal_places=2, default=0, null=True)
    discount_unit = fields.CharField(max_length=20, null=True)
    effective_from = fields.DatetimeField(null=True)
    effective_until = fields.DatetimeField(null=True)

    # Foreign key relations
    category = fields.ForeignKeyField(
        "models.ServiceCategory",
        related_name="services",
        null=True,
        on_delete=fields.RESTRICT,
    )

    # Reverse relations
    appointments: fields.ReverseRelation["Appointment"]
    package_services: fields.ReverseRelation["PackageService"]
    service_skills: fields.ReverseRelation["ServiceSkill"]

    class Meta:
        table = "services"

    def __str__(self):
        return f"service_id: {self.id}, Service_name: {self.name}, Category: {self.category} ,Amount: ${self.amount}, Duration: {self.duration_minutes} Minutes"

    class Meta:
        table = "services"
        indexes = [("status",), ("name",)]

    @classmethod
    async def get_active_services(cls, service_category_id: str) -> List["Service"]:
        """Get all active services"""
        return await cls.filter(category=service_category_id).prefetch_related(
            "service_skills__skill"
        )

    @classmethod
    async def get_service_detail(cls, service_id: str) -> "Service":
        """Get service details"""
        return await cls.filter(id=service_id).first()


class Package(Model):
    """Service packages/bundles"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    name = fields.CharField(max_length=255)
    type = fields.CharField(max_length=255)
    description = fields.TextField(null=True)
    total_amount = fields.DecimalField(max_digits=10, decimal_places=2)
    total_duration_minutes = fields.IntField(null=True)
    discount = fields.DecimalField(max_digits=10, decimal_places=2, default=0)
    discount_unit = fields.CharField(max_length=20, default="price")
    is_active = fields.BooleanField(default=True, null=True)
    is_featured = fields.BooleanField(default=True, null=True)
    created_at = fields.DatetimeField(auto_now_add=True, null=True)
    updated_at = fields.DatetimeField(auto_now=True, null=True)

    # Reverse relations
    appointments: fields.ReverseRelation["Appointment"]
    package_services: fields.ReverseRelation["PackageService"]

    class Meta:
        table = "packages"

    @classmethod
    async def get_active_packages(cls):
        return await cls.filter(is_active=True)


class PackageService(Model):
    """Many-to-many relationship between packages and services"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    created_at = fields.DatetimeField(auto_now_add=True, null=True)

    # Foreign key relations
    package = fields.ForeignKeyField(
        "models.Package", related_name="package_services", on_delete=fields.CASCADE
    )
    service = fields.ForeignKeyField(
        "models.Service", related_name="package_services", on_delete=fields.CASCADE
    )

    class Meta:
        table = "package_services"
        unique_together = (("package", "service"),)
