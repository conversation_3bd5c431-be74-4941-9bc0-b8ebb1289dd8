from typing import Literal
from pydantic import PostgresDsn, field_validator
from pydantic_settings import BaseSettings
from livekit.plugins.elevenlabs import TTSModels
from livekit.plugins.openai import STTModels
from livekit.plugins.openai.llm import ChatModels


class Settings(BaseSettings):
    # Database
    DATABASE_HOST: str
    DATABASE_PORT: int = 5432
    DATABASE_USER: str
    DATABASE_PASSWORD: str
    DATABASE_DB: str

    DB_MIN_CONNECTIONS: int = 1
    DB_MAX_CONNECTIONS: int = 10
    DB_CONNECTION_TIMEOUT: int = 60

    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True

    # API Keys
    GOOGLE_API_KEY: str
    OPENAI_API_KEY: str
    ELEVEN_API_KEY: str
    DEEPGRAM_API_KEY: str
    HUME_API_KEY: str
    LIVEKIT_API_KEY: str
    LIVEKIT_API_SECRET: str
    LIVEKIT_URL: str
    LIVEKIT_SIP_URI: str

    # AWS KEYS
    AWS_BUCKET_NAME: str
    AWS_REGION: str
    AWS_ACCESS_KEY_ID: str
    AWS_SECRET_ACCESS_KEY: str

    # Twilio
    TWILIO_ACCOUNT_SID: str
    TWILIO_AUTH_TOKEN: str
    TWILIO_PHONE_NUMBER: str
    TWIML_USERNAME: str
    TWIML_PASSWORD: str

    # Application
    LOG_LEVEL: str = "INFO"
    RECORDINGS_PATH: str = "recordings/{room_name}/recording.ogg"
    TRANSCRIPT_FILE_PATH: str = "{file_path}/transcript.json"
    SUMMARY_FILE_PATH: str = "{file_path}/summary.txt"
    AGENT_BACKGROUND_AUDIO: bool = False

    #REDIS
    REDIS_HOST : str
    REDIS_PORT : int = 6379
    REDIS_DB : str

    #Prometheus
    PROMETHEUS_PORT: int = 8000

    @field_validator("DEBUG")
    def debug_mode(cls, v, values):
        if values.data.get("ENVIRONMENT") == "development":
            return True
        return v

    class Config:
        env_file = ".env"
        case_sensitive = True


class AgentSettings(BaseSettings):

    AGENT_NAME: str = "Rachel"

    """STT & TTS Model settings"""
    STT_MODEL: STTModels = "whisper-1"
    # TTS_MODEL: TTSModels = "eleven_flash_v2_5"
    TTS_MODEL: str = "aura-2-helena-en"
    # TTS_MODEL: str = "aura-luna-en"
    TTS_VOICE_ID: str = "TX3LPaxmHKxFdv7VOQHJ"
    LLM_MODEL: ChatModels | Literal["gpt-4.1-nano-2025-04-14"] = (
        "gpt-4.1-nano-2025-04-14"
    )
    LLM_SUMMARY_MODEL: ChatModels = "gpt-4o-mini"


settings = Settings()
agent_settings = AgentSettings()
